# 地址管理功能测试文档

## 数据库表创建

首先执行SQL脚本创建相关表：

```sql
-- 执行 fuguang-api/sql/ry_20250522.sql 中的地址管理相关SQL
```

## 管理后台测试

### 1. 登录管理后台
- 访问：http://localhost:8888
- 用户名：admin
- 密码：admin123

### 2. 菜单权限验证
- 登录后应该能看到"浮光壁垒"菜单
- 点击展开应该能看到"地址管理"子菜单

### 3. 地址管理功能测试

#### 3.1 查看地址列表
- 访问地址管理页面
- 验证列表显示正常
- 测试搜索功能

#### 3.2 新增地址
测试数据：
```json
{
  "userId": 1000,
  "contactName": "张三",
  "contactPhone": "13800138000",
  "contactSex": "0",
  "province": "广东省",
  "city": "深圳市",
  "district": "南山区",
  "address": "科技园南区深南大道10000号",
  "longitude": "113.9177",
  "latitude": "22.5431",
  "isDefault": "1",
  "status": "0"
}
```

#### 3.3 修改地址
- 选择一条地址记录进行修改
- 验证数据回显正常
- 修改部分字段后保存

#### 3.4 删除地址
- 选择地址记录进行删除
- 验证逻辑删除功能

#### 3.5 设置默认地址
- 测试设置默认地址功能
- 验证只能有一个默认地址

#### 3.6 状态切换
- 测试启用/停用功能

## APP端API测试

### 1. 获取用户地址列表
```
GET /app/address/list
Headers: Authorization: Bearer {token}
```

### 2. 新增地址
```
POST /app/address
Headers: Authorization: Bearer {token}
Content-Type: application/json

{
  "contactName": "李四",
  "contactPhone": "13900139000",
  "contactSex": "1",
  "province": "北京市",
  "city": "北京市",
  "district": "朝阳区",
  "address": "建国门外大街1号",
  "longitude": "116.4074",
  "latitude": "39.9042",
  "isDefault": "0",
  "status": "0"
}
```

### 3. 修改地址
```
PUT /app/address
Headers: Authorization: Bearer {token}
Content-Type: application/json

{
  "addressId": 1,
  "contactName": "李四",
  "contactPhone": "13900139001",
  "contactSex": "1",
  "province": "北京市",
  "city": "北京市",
  "district": "朝阳区",
  "address": "建国门外大街2号",
  "longitude": "116.4074",
  "latitude": "39.9042",
  "isDefault": "0",
  "status": "0"
}
```

### 4. 删除地址
```
DELETE /app/address/{addressId}
Headers: Authorization: Bearer {token}
```

### 5. 设置默认地址
```
PUT /app/address/setDefault/{addressId}
Headers: Authorization: Bearer {token}
```

### 6. 获取默认地址
```
GET /app/address/default
Headers: Authorization: Bearer {token}
```

### 7. 获取地址详情
```
GET /app/address/{addressId}
Headers: Authorization: Bearer {token}
```

## 测试要点

### 1. 数据验证
- 联系人姓名不能为空
- 联系人手机号不能为空且格式正确
- 详细地址不能为空
- 同一用户下联系人手机号不能重复

### 2. 权限验证
- APP端用户只能操作自己的地址
- 管理后台需要相应权限才能访问

### 3. 业务逻辑
- 设置默认地址时会自动取消其他默认地址
- 删除采用逻辑删除
- 完整地址自动拼接

### 4. 异常处理
- 参数校验
- 权限校验
- 数据不存在的处理

## 预期结果

1. 管理后台能正常显示地址管理菜单
2. 地址的增删改查功能正常
3. APP端API能正常响应
4. 数据验证和权限控制正常
5. 默认地址设置逻辑正确
