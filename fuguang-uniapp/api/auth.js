// 认证相关API
import { post, get, publicGet } from "@/utils/request";

// 用户登录
export const login = (data) => {
  return post("/app/login", data);
};

// 用户注册
export const register = (data) => {
  return post("/app/register", data);
};

// 获取用户信息
export const getUserInfo = () => {
  return get("/app/user/profile");
};

// 修改用户信息
export const updateUserInfo = (data) => {
  return post("/app/user/profile", data);
};

// 修改密码
export const updatePassword = (data) => {
  return post("/app/user/updatePwd", data);
};

// 上传头像
export const uploadAvatar = (filePath) => {
  return new Promise((resolve, reject) => {
    const token = uni.getStorageSync("token");
    const config = require("@/config/index.js").default;

    uni.uploadFile({
      url: config.baseURL + "/app/user/avatar",
      filePath,
      name: "avatarfile",
      header: {
        Authorization: token ? `Bearer ${token}` : "",
      },
      success: (res) => {
        const data = JSON.parse(res.data);
        if (data.code === 200) {
          resolve(data);
        } else {
          uni.showToast({
            title: data.msg || "上传失败",
            icon: "none",
          });
          reject(data);
        }
      },
      fail: (err) => {
        uni.showToast({
          title: "上传失败",
          icon: "none",
        });
        reject(err);
      },
    });
  });
};

// 实名认证
export const realNameAuth = (data) => {
  return post("/app/user/auth", data);
};

// 更新位置信息
export const updateLocation = (data) => {
  return post("/app/user/location", data);
};

// 发送验证码
export const sendSmsCode = (data) => {
  return post("/app/sms/send", data);
};

// 验证码登录
export const smsLogin = (data) => {
  return post("/app/sms/login", data);
};

// 获取用户协议（公共接口，无需登录）
export const getUserAgreement = () => {
  return publicGet("/app/agreement/user");
};

// 获取隐私政策（公共接口，无需登录）
export const getPrivacyPolicy = () => {
  return publicGet("/app/agreement/privacy");
};

// 获取用户分数信息（信任分、执行分）
export const getUserScores = () => {
  return get("/app/user/scores");
};

// 获取用户财务信息（保障金、佣金）
export const getUserFinance = () => {
  return get("/app/user/finance");
};

// 获取用户中心功能配置
export const getUserCenterFunctions = () => {
  return get("/app/user/functions");
};

// 重置密码（通过验证码）
export const resetPassword = (data) => {
  return post("/app/user/resetPwd", data);
};

// 注销账号
export const deleteAccount = (data) => {
  return post("/app/user/delete", data);
};
